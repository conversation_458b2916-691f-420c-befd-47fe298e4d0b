// Generated by React Router

import "react-router"

declare module "react-router" {
  interface Register {
    pages: Pages
    routeFiles: RouteFiles
  }
}

type Pages = {
  "/": {
    params: {};
  };
  "/login": {
    params: {};
  };
  "/dashboard": {
    params: {};
  };
};

type RouteFiles = {
  "root.tsx": {
    id: "root";
    page: "/" | "/login" | "/dashboard";
  };
  "routes/home.tsx": {
    id: "routes/home";
    page: "/";
  };
  "routes/login.tsx": {
    id: "routes/login";
    page: "/login";
  };
  "routes/dashboard.tsx": {
    id: "routes/dashboard";
    page: "/dashboard";
  };
};