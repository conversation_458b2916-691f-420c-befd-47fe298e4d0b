import React, { createContext, useContext, type ReactNode } from "react";
import { useAuth } from "../../hooks/use-auth";
import type { AuthSession, User } from "../../lib/auth-client";

interface AuthContextType {
  // State
  session: AuthSession;
  user: User | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;

  // Actions
  signIn: (email: string, password: string) => Promise<any>;
  signUp: (email: string, password: string, name?: string) => Promise<any>;
  signOut: () => Promise<void>;
  refreshSession: () => Promise<void>;
  clearError: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const auth = useAuth();

  return (
    <AuthContext.Provider value={auth}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuthContext() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuthContext must be used within an AuthProvider");
  }
  return context;
}

// Higher-order component for protecting routes
interface ProtectedRouteProps {
  children: ReactNode;
  fallback?: ReactNode;
  redirectTo?: string;
}

export function ProtectedRoute({ 
  children, 
  fallback = <div>Please sign in to access this page.</div>,
  redirectTo 
}: ProtectedRouteProps) {
  const { isAuthenticated, loading } = useAuthContext();

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!isAuthenticated) {
    if (redirectTo && typeof window !== "undefined") {
      window.location.href = redirectTo;
      return null;
    }
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

// Component for showing content only to authenticated users
interface AuthenticatedOnlyProps {
  children: ReactNode;
  fallback?: ReactNode;
}

export function AuthenticatedOnly({ children, fallback = null }: AuthenticatedOnlyProps) {
  const { isAuthenticated, loading } = useAuthContext();

  if (loading) {
    return null;
  }

  return isAuthenticated ? <>{children}</> : <>{fallback}</>;
}

// Component for showing content only to unauthenticated users
interface UnauthenticatedOnlyProps {
  children: ReactNode;
  fallback?: ReactNode;
}

export function UnauthenticatedOnly({ children, fallback = null }: UnauthenticatedOnlyProps) {
  const { isAuthenticated, loading } = useAuthContext();

  if (loading) {
    return null;
  }

  return !isAuthenticated ? <>{children}</> : <>{fallback}</>;
}
