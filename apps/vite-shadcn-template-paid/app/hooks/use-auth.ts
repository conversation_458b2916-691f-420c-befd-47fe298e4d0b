import { useState, useEffect, useCallback } from "react";
import { authAPI, type AuthSession, type User } from "../lib/auth-client";

export function useAuth() {
  const [session, setSession] = useState<AuthSession>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadSession = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const sessionData = await authAPI.getSession();
      setSession(sessionData as AuthSession);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load session");
      setSession(null);
    } finally {
      setLoading(false);
    }
  }, []);

  // Load session on mount
  useEffect(() => {
    loadSession();
  }, [loadSession]);

  const signIn = useCallback(async (email: string, password: string) => {
    try {
      setLoading(true);
      setError(null);
      const result = await authAPI.signIn(email, password);
      await loadSession(); // Reload session after sign in
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Sign in failed";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [loadSession]);

  const signUp = useCallback(async (email: string, password: string, name?: string) => {
    try {
      setLoading(true);
      setError(null);
      const result = await authAPI.signUp(email, password, name);
      await loadSession(); // Reload session after sign up
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Sign up failed";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [loadSession]);

  const signOut = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      await authAPI.signOut();
      setSession(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Sign out failed";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const refreshSession = useCallback(async () => {
    await loadSession();
  }, [loadSession]);

  return {
    // State
    session,
    user: session?.user || null,
    isAuthenticated: !!session,
    loading,
    error,

    // Actions
    signIn,
    signUp,
    signOut,
    refreshSession,
    clearError: () => setError(null),
  };
}

// Hook for checking if user is authenticated (simpler version)
export function useIsAuthenticated() {
  const { isAuthenticated, loading } = useAuth();
  return { isAuthenticated, loading };
}

// Hook for getting current user (simpler version)
export function useUser(): { user: User | null; loading: boolean } {
  const { user, loading } = useAuth();
  return { user, loading };
}
