import { create<PERSON>uth<PERSON>lient } from "better-auth/react";

// Create the auth client for frontend use
export const authClient = createAuthClient({
  baseURL: typeof window !== "undefined" ? window.location.origin : "http://localhost:3000",
  fetchOptions: {
    credentials: "include", // Required for sending cookies cross-origin
  },
});

// Export commonly used auth methods for convenience
export const {
  signIn,
  signUp,
  signOut,
  useSession,
  getSession,
} = authClient;

// Auth API client for manual requests
export const authAPI = {
  // Sign in with email and password
  async signIn(email: string, password: string) {
    try {
      const response = await fetch("/api/auth/sign-in/email", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({ email, password }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error((error as any)?.message || "Sign in failed");
      }

      return await response.json();
    } catch (error) {
      console.error("Sign in error:", error);
      throw error;
    }
  },

  // Sign up with email and password
  async signUp(email: string, password: string, name?: string) {
    try {
      const response = await fetch("/api/auth/sign-up/email", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({ email, password, name }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error((error as any)?.message || "Sign up failed");
      }

      return await response.json();
    } catch (error) {
      console.error("Sign up error:", error);
      throw error;
    }
  },

  // Sign out
  async signOut() {
    try {
      const response = await fetch("/api/auth/sign-out", {
        method: "POST",
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error("Sign out failed");
      }

      return await response.json();
    } catch (error) {
      console.error("Sign out error:", error);
      throw error;
    }
  },

  // Get current session
  async getSession() {
    try {
      const response = await fetch("/api/auth/get-session", {
        method: "GET",
        credentials: "include",
      });

      if (!response.ok) {
        return null;
      }

      return await response.json();
    } catch (error) {
      console.error("Get session error:", error);
      return null;
    }
  },
};

// Types for better TypeScript support
export type User = {
  id: string;
  email: string;
  name: string;
  emailVerified: boolean;
  image?: string;
  createdAt: Date;
  updatedAt: Date;
};

export type Session = {
  id: string;
  userId: string;
  expiresAt: Date;
  token: string;
  ipAddress?: string;
  userAgent?: string;
};

export type AuthSession = {
  user: User;
  session: Session;
} | null;
