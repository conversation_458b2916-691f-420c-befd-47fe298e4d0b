import type { Route } from "./+types/home";
import { Welcome } from "../welcome/welcome";
import { AuthenticatedOnly, UnauthenticatedOnly, useAuthContext } from "../components/auth/auth-provider";

export function meta({}: Route.MetaArgs) {
    return [
        { title: "React Router + Hono + Better Auth Template" },
        { name: "description", content: "A modern full-stack template with authentication" },
    ];
}

export function loader({ context }: Route.LoaderArgs) {
    return { message: (context as any)?.cloudflare?.env?.VALUE_FROM_CLOUDFLARE || "Hello from Cloudflare" };
}

function AuthNavigation() {
    const { user, signOut } = useAuthContext();

    const handleSignOut = async () => {
        try {
            await signOut();
            window.location.reload();
        } catch (error) {
            console.error("Sign out error:", error);
        }
    };

    return (
        <div className="fixed top-4 right-4 z-10">
            <AuthenticatedOnly>
                <div className="bg-white shadow-lg rounded-lg p-4 border">
                    <div className="flex items-center space-x-4">
                        <div className="text-sm">
                            <p className="font-medium text-gray-900">
                                {user?.name || user?.email}
                            </p>
                            <p className="text-gray-500">Signed in</p>
                        </div>
                        <div className="flex space-x-2">
                            <a
                                href="/dashboard"
                                className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm font-medium transition duration-200"
                            >
                                Dashboard
                            </a>
                            <button
                                onClick={handleSignOut}
                                className="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-sm font-medium transition duration-200"
                            >
                                Sign Out
                            </button>
                        </div>
                    </div>
                </div>
            </AuthenticatedOnly>

            <UnauthenticatedOnly>
                <div className="bg-white shadow-lg rounded-lg p-4 border">
                    <div className="flex items-center space-x-4">
                        <div className="text-sm">
                            <p className="font-medium text-gray-900">Welcome!</p>
                            <p className="text-gray-500">Try the authentication</p>
                        </div>
                        <div className="flex space-x-2">
                            <a
                                href="/login"
                                className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm font-medium transition duration-200"
                            >
                                Sign In
                            </a>
                        </div>
                    </div>
                </div>
            </UnauthenticatedOnly>
        </div>
    );
}

export default function Home({ loaderData }: Route.ComponentProps) {
    return (
        <div className="relative">
            <AuthNavigation />
            <Welcome message={loaderData.message} />
        </div>
    );
}