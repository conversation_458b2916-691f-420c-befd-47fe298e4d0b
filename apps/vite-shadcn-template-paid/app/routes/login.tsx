import type { Route } from "./+types/login";
import { LoginForm } from "../components/auth/login-form";
import { UnauthenticatedOnly } from "../components/auth/auth-provider";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Login - React Router + Hono Template" },
    { name: "description", content: "Sign in to your account" },
  ];
}

export default function Login() {
  const handleLoginSuccess = () => {
    // Redirect to dashboard or home page after successful login
    window.location.href = "/dashboard";
  };

  const handleLoginError = (error: string) => {
    console.error("Login error:", error);
    // Error is already handled by the LoginForm component
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <h1 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Welcome Back
        </h1>
        <p className="mt-2 text-center text-sm text-gray-600">
          Sign in to your account to continue
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <UnauthenticatedOnly
          fallback={
            <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
              <div className="text-center">
                <h2 className="text-lg font-medium text-gray-900 mb-4">
                  You're already signed in!
                </h2>
                <p className="text-sm text-gray-600 mb-6">
                  You can go to your dashboard or sign out to use a different account.
                </p>
                <div className="space-y-3">
                  <a
                    href="/dashboard"
                    className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Go to Dashboard
                  </a>
                  <a
                    href="/"
                    className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Go to Home
                  </a>
                </div>
              </div>
            </div>
          }
        >
          <LoginForm
            onSuccess={handleLoginSuccess}
            onError={handleLoginError}
            showSignUp={true}
          />
        </UnauthenticatedOnly>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Authentication Demo
          </h3>
          <div className="text-sm text-gray-600 space-y-2">
            <p>
              <strong>Features included:</strong>
            </p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>Email and password authentication</li>
              <li>User registration and login</li>
              <li>Session management with cookies</li>
              <li>Protected routes and components</li>
              <li>Better Auth + Hono integration</li>
              <li>TypeScript support</li>
            </ul>
            <p className="mt-4">
              <strong>Try it out:</strong> Create an account or sign in to see the authentication in action.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
