{"name": "react-router-hono-fullstack-template", "description": "A modern full-stack template powered by Cloudflare Workers, using Hono for backend APIs, React Router for frontend routing, and shadcn/ui for beautiful, accessible components styled with Tailwind CSS", "private": true, "type": "module", "cloudflare": {"label": "React Router + Hono Fullstack App", "products": ["Workers"], "categories": ["starter"], "docs_url": "https://developers.cloudflare.com/workers", "preview_image_url": "https://imagedelivery.net/wSMYJvS3Xw-n339CbDyDIA/cd71c67a-253f-477d-022c-2f90cb4b3d00/public", "icon_urls": [], "publish": true}, "dependencies": {"hono": "^4.8.12", "isbot": "^5.1.29", "react": "^19.1.1", "react-dom": "^19.1.1", "react-router": "^7.7.1", "better-auth": "^1.3.4"}, "devDependencies": {"@cloudflare/vite-plugin": "^1.11.1", "@cloudflare/workers-types": "^4.********.0", "@react-router/dev": "^7.7.1", "@tailwindcss/vite": "^4.1.11", "@types/node": "^24.2.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "tailwindcss": "^4.1.11", "typescript": "^5.9.2", "vite": "^6.3.5", "vite-tsconfig-paths": "^5.1.4", "wrangler": "4.27.0"}, "scripts": {"build": "react-router build", "cf-typegen": "wrangler types", "deploy": "bun run build && wrangler deploy", "dev": "react-router dev", "preview": "bun run build && vite preview", "typecheck": "bun run cf-typegen && react-router typegen && tsc -b", "auth:generate": "bunx @better-auth/cli@latest generate --config app/auth/index.ts --output app/db/auth.schema.ts -y", "db:generate": "drizzle-kit generate", "db:migrate": "wrangler d1 migrations apply DB"}}