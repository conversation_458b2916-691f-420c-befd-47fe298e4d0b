import { Hono } from "hono";
import { cors } from "hono/cors";
import { createRequestHandler } from "react-router";
import { drizzle } from "drizzle-orm/d1";
import { createAuth } from "../app/auth";

type Bindings = {
    DB: D1Database;
    BETTER_AUTH_SECRET: string;
    BETTER_AUTH_URL?: string;
};

const app = new Hono<{ Bindings: Bindings }>();

// Configure CORS for authentication endpoints
app.use(
    "/api/auth/*",
    cors({
        origin: (origin) => {
            // Allow localhost for development
            if (origin?.includes("localhost") || origin?.includes("127.0.0.1")) {
                return origin;
            }
            // Add your production domains here
            return origin || "*";
        },
        allowHeaders: ["Content-Type", "Authorization"],
        allowMethods: ["POST", "GET", "OPTIONS"],
        exposeHeaders: ["Content-Length"],
        maxAge: 600,
        credentials: true,
    }),
);

// Better Auth API routes
app.on(["POST", "GET"], "/api/auth/*", async (c) => {
    const db = drizzle(c.env.DB);
    const authInstance = createAuth(db);

    return authInstance.handler(c.req.raw);
});

// Add more API routes here

// React Router handler for all other routes
app.get("*", (c) => {
    const requestHandler = createRequestHandler(
        // @ts-ignored
        () => import("virtual:react-router/server-build"),
        import.meta.env.MODE,
    );

    return requestHandler(c.req.raw, {
        cloudflare: { env: c.env, ctx: c.executionCtx },
    });
});

export default app;